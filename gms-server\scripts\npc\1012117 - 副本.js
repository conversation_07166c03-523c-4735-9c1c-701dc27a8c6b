
var selec_style, selec_final;
//膚色
var skin_color = Array(0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 16, 18, 19, 20, 21, 22, 23, 28, 29);
//眼睛顏色(無須新增) 用於陣列佔存
var eyes_color = Array();
//髮色(無須新增) 用於陣列佔存
var hair_color = Array();
/*
髮型每10為單位
女髮 1 4 7 8
男髮 0 3 5 6
共通使用 2 9
 */
//女髮型1(只需要最頭ID)
var F_hair_1 = Array(31000, 31010, 31020, 31030, 31040, 31050, 31060, 31070, 31080, 31090, 31100, 31110, 31120, 31130, 31140, 31150, 31160, 31170, 31180, 31190, 31200, 31210, 31220, 31230, 31240, 31250, 31260, 31270, 31280, 31290, 31300, 31310, 31320, 31330, 31340, 31350, 31360, 31370, 31380, 31400, 31410, 31420, 31430, 31440, 31450, 31460, 31470, 31480, 31490, 31510, 31520, 31530, 31540, 31550, 31560, 31570, 31580, 31590, 31600, 31610, 31620, 31630, 31640, 31650, 31660, 31670, 31680, 31690, 31700, 31710, 31720, 31730, 31740, 31750, 31760, 31770, 31780, 31790, 31800, 31810, 31820, 31830, 31840, 31850, 31860, 31870, 31880, 31890, 31900, 31910, 31920, 31930, 31940, 31950, 31960, 31970, 31990);
//女髮型2(只需要最頭ID)
var F_hair_2 = Array(34000, 34010, 34020, 34030, 34040, 34050, 34060, 34070, 34080, 34090, 34100, 34110, 34120, 34130, 34140, 34150, 34160, 34170, 34180, 34190, 34200, 34210, 34220, 34230, 34240, 34250, 34260, 34270, 34280, 34290, 34300, 34310, 34320, 34330, 34340, 34350, 34360, 34370, 34380, 34400, 34410, 34420, 34430, 34440, 34450, 34470, 34480, 34490, 34510, 34540, 34560, 34580, 34590, 34600, 34610, 34620, 34630, 34640, 34650, 34660, 34670, 34680, 34690, 34700, 34710, 34720, 34730, 34740, 34750, 34760, 34770, 34780, 34790, 34800, 34810, 34820, 34830, 34840, 34850, 34860, 34870, 34880, 34890, 34900, 34910, 34940, 34950, 34960, 34970, 34980, 34990);
//女髮型3(只需要最頭ID)
var F_hair_3 = Array(37000, 37010, 37020, 37030, 37040, 37050, 37060, 37070, 37080, 37090, 37100, 37110, 37120, 37130, 37140, 37150, 37160, 37170, 37180, 37190, 37200, 37210, 37220, 37230, 37240, 37250, 37260, 37270, 37280, 37290, 37300, 37310, 37320, 37330, 37340, 37350, 37370, 37380, 37400, 37410, 37420, 37430, 37440, 37450, 37460, 37470, 37480, 37490, 37500, 37510, 37520, 37530, 37540, 37550, 37560, 37570, 37580, 37590, 37600, 37610, 37620, 37630, 37640, 37650, 37660, 37670, 37680, 37690, 37700, 37710, 37720, 37730, 37740, 37750, 37760, 37770, 37780, 37790, 37800, 37810, 37820, 37830, 37840, 37850, 37860, 37870, 37880, 37890, 37900, 37910, 37920, 37930, 37940, 37950, 37960, 37970, 37980, 37990);
//女髮型4(只需要最頭ID)
var F_hair_4 = Array(38000, 38010, 38020, 38030, 38040, 38050, 38060, 38070, 38080, 38090, 38100, 38110, 38120, 38130, 38140, 38150, 38160, 38170, 38190, 38200, 38210, 38220, 38230, 38240, 38250, 38260, 38270, 38280, 38290, 38300, 38310, 38320, 38330, 38340, 38350, 38360, 38370, 38380, 38390, 38400, 38410, 38420, 38430, 38440, 38450, 38460, 38470, 38480, 38490, 38500, 38520, 38530, 38540, 38550, 38560, 38570, 38580, 38590, 38600, 38610, 38620, 38630, 38640, 38650, 38660, 38670, 38680, 38690, 38700, 38710, 38730, 38740, 38750, 38760, 38770, 38780, 38790, 38800, 38810, 38820, 38830, 38840, 38850, 38860, 38880, 38890, 38900, 38910, 38920, 38930, 38940);
//女发5
var F_hair_102 = Array(41060,41070,41080,41090,41100,41110,41120,41130,41140,41150,41160,41190,41200,41210,41220,41340,41350,41360,41370,41380,41390,41400,41410,41420,41430,41440,41450,41460,41470,41480,41490,41510,41520,41530,41550,41560,41570,41580,41590,41600,41610,41620,41630,41640,41650,41660,41670,41680,41690,41700,41710,41720,41730,41740,41750,41760,41770,41780,41790,41800,41810,41820,41830,41840,41850,41860,41870,41880,41890,41900,41910,41920,41930,41940,41950,41960,41970,41980,41990,42000,42010,42030,42040,42060,42070,42080,42090,40950,40970,40980);
//女发6
var F_hair_103 = Array(61000,61010,61020,61030,61040,61050,61060,61070,61080,61090,61100,61110,61120,61130,61140,61150,61160,61170,61180,61190,61200,61210,61220,61230,61240,61250,61260,61270,61280,61290,61300,61310,61320,61330,61340,61350,61360,61370,61380,61390,61400,61410,61420,61430,61440,61450,61460,61470,61480,61490,61500,61510,61520,61530,61550,61560,61570,61580,61590,61630,61640,61650,61660,61670,61680,61690,61700,61710,61720,61730,61740,61750,61760,61770,61780,61790,61800,61830,61840,61850,61860,61870,61880,61890,61900,61910,61920,61930,61970,61980,64020,64030);
//男髮型1(只需要最頭ID)
var M_hair_1 = Array(30000, 30010, 30020, 30030, 30040, 30050, 30060, 30070, 30080, 30090, 30100, 30110, 30120, 30130, 30140, 30150, 30160, 30170, 30180, 30190, 30200, 30210, 30220, 30230, 30240, 30250, 30260, 30270, 30280, 30290, 30300, 30310, 30320, 30330, 30340, 30350, 30360, 30370, 30380, 30400, 30410, 30420, 30430, 30440, 30450, 30460, 30470, 30480, 30490, 30510, 30520, 30530, 30540, 30550, 30560, 30570, 30580, 30590, 30600, 30610, 30620, 30630, 30640, 30650, 30660, 30670, 30680, 30690, 30700, 30710, 30720, 30730, 30740, 30750, 30760, 30770, 30780, 30790, 30800, 30810, 30820, 30830, 30840, 30850, 30860, 30870, 30880, 30890, 30900, 30910, 30920, 30930, 30940, 30950, 30960, 30970, 30990);
//男髮型2(只需要最頭ID)
var M_hair_2 = Array(33000, 33010, 33020, 33030, 33040, 33050, 33060, 33070, 33080, 33090, 33100, 33110, 33120, 33130, 33140, 33150, 33160, 33170, 33180, 33190, 33200, 33210, 33220, 33230, 33240, 33250, 33260, 33270, 33280, 33290, 33300, 33310, 33320, 33330, 33340, 33350, 33360, 33370, 33380, 33390, 33400, 33410, 33430, 33440, 33450, 33460, 33470, 33480, 33500, 33510, 33520, 33530, 33540, 33550, 33580, 33590, 33600, 33610, 33620, 33630, 33640, 33650, 33660, 33670, 33680, 33690, 33700, 33710, 33720, 33730, 33740, 33750, 33760, 33770, 33780, 33790, 33800, 33810, 33820, 33830, 33930, 33940, 33950, 33960, 33970, 33980, 33990);
//男髮型3(只需要最頭ID)
var M_hair_3 = Array(35000, 35010, 35020, 35030, 35040, 35050, 35060, 35070, 35080, 35090, 35100, 35110, 35120, 35130, 35140, 35150, 35160, 35170, 35180, 35190, 35200, 35210, 35220, 35230, 35240, 35250, 35260, 35270, 35280, 35290, 35300, 35310, 35330, 35340, 35350, 35360, 35400, 35410, 35420, 35430, 35440, 35450, 35460, 35470, 35480, 35490, 35500, 35510, 35520, 35530, 35540, 35550, 35560, 35570, 35580, 35590, 35600, 35620, 35630, 35640, 35650, 35660, 35670, 35680, 35690, 35700, 35710, 35720, 35730, 35740, 35750, 35760, 35770, 35780, 35790, 35800, 35820, 35830, 35950, 35960);
//男髮型4(只需要最頭ID)
var M_hair_4 = Array(36000, 36010, 36020, 36030, 36040, 36050, 36060, 36070, 36080, 36090, 36100, 36110, 36120, 36130, 36140, 36150, 36160, 36170, 36180, 36190, 36200, 36210, 36220, 36230, 36240, 36250, 36260, 36270, 36280, 36290, 36300, 36310, 36320, 36330, 36340, 36350, 36360, 36370, 36380, 36390, 36400, 36410, 36420, 36430, 36440, 36450, 36460, 36470, 36480, 36490, 36500, 36510, 36520, 36530, 36540, 36550, 36560, 36570, 36580, 36590, 36600, 36610, 36620, 36630, 36640, 36650, 36670, 36680, 36690, 36700, 36710, 36720, 36730, 36740, 36750, 36760, 36770, 36780, 36790, 36800, 36810, 36820, 36830, 36840, 36850, 36860, 36870, 36880, 36890, 36900, 36910, 36920, 36930, 36940, 36950, 36960, 36980);
var M_hair_100 = Array(40000,40010,40020,40030,40040,40050,40060,40070,40080,40090,40100,40110,40120,40250,40260,40270,40280,40290,40300,40310,40320,40330,40340,40350,40360,40370,40380,40390,40400,40410,40420,40430,40440,40450,40460,40470,40480,40490,40500,40520,40530,40540,40550,40560,40570,40580,40590,40600,40610,40620,40630,40640,40650,40660,40670,40680,40690,40700,40710,40730,40740,40750,40760,40770,40790,40800,40810,40820,40830,40840,40890,40910,40930);
//男发6
var M_hair_101 = Array(60000,60010,60020,60030,60040,60050,60060,60070,60080,60090,60100,60110,60120,60130,60140,60150,60160,60170,60180,60190,60200,60210,60220,60230,60240,60250,60260,60270,60280,60290,60300,60310,60320,60330,60340,60350,60360,60370,60380,60390,60400,60420,60430,60440,60450,60460,60470,60480,60490,60500,60510,60520,60530,60540,60550,60580,60590,60600,60610,60620);
//共通髮型1(只需要最頭ID)
var C_hair_1 = Array(32000, 32010, 32020, 32030, 32040, 32050, 32060, 32070, 32080, 32090, 32100, 32110, 32120, 32130, 32140, 32150, 32160, 32170, 32180, 32190, 32200, 32210, 32220, 32230, 32240, 32250, 32260, 32270, 32280, 32290, 32300, 32310, 32320, 32330, 32340, 32350, 32360, 32370, 32380, 32390, 32400, 32410, 32420, 32520, 32530, 32540, 32550, 32560, 32570, 32580, 32590, 32600, 32610, 32620, 32630, 32640, 32650, 32660, 32670, 32680, 32690, 32700, 32710, 32720, 32730, 32740, 32750, 32760, 32770, 32780, 32790, 32800, 32820, 32830, 32840, 32850, 32860, 32870, 32880, 32890, 32900, 32910, 32920, 32930);
//共通髮型2(只需要最頭ID)
var C_hair_2 = Array(39090, 39100, 39110, 39120, 39130, 39140, 39150, 39160, 39170, 39180, 39250, 39260, 39270, 39280, 39290, 39300, 39310, 39320, 39330, 39340, 39350, 39360, 39370, 39380, 39390, 39400, 39410, 39420, 39430, 39440, 39450, 39460, 39470, 39480);

/*
臉型0~99
女臉 1 4 6 8
男臉 0 3 5 7
共通使用
 */

//女臉型1(只需要最頭ID)
var F_face_1 = Array(21000, 21001, 21002, 21003, 21004, 21005, 21006, 21007, 21008, 21009, 21010, 21011, 21012, 21013, 21014, 21015, 21016, 21017, 21018, 21019, 21020, 21021, 21022, 21023, 21024, 21025, 21026, 21027, 21028, 21029, 21030, 21031, 21033, 21034, 21035, 21036, 21037, 21038, 21041, 21042, 21043, 21044, 21045, 21046, 21047, 21048, 21049, 21050, 21052, 21053, 21054, 21055, 21056, 21057, 21058, 21059, 21060, 21061, 21062, 21063, 21064, 21065, 21068, 21069, 21070, 21071, 21072, 21073, 21074, 21075, 21076, 21077, 21078, 21079, 21080, 21081, 21082, 21083, 21084, 21085, 21086, 21087, 21088, 21089, 21090, 21091, 21092, 21093, 21094, 21095, 21096, 21097, 21098, 21099);
//女臉型2(只需要最頭ID)
var F_face_2 = Array(24001, 24002, 24003, 24004, 24007, 24008, 24009, 24010, 24011, 24012, 24013, 24014, 24015, 24016, 24017, 24018, 24019, 24020, 24022, 24024, 24025, 24026, 24027, 24028, 24029, 24030, 24031, 24032, 24035, 24036, 24037, 24038, 24039, 24040, 24041, 24050, 24051, 24052, 24053, 24054, 24055, 24056, 24057, 24058, 24059, 24060, 24061, 24063, 24064, 24066, 24067, 24068, 24069, 24070, 24071, 24072, 24073, 24074, 24075, 24076, 24077, 24078, 24079, 24080, 24081, 24082, 24083, 24084, 24085, 24086, 24087, 24088, 24089, 24090, 24091, 24092, 24093, 24094, 24095, 24096, 24097, 24098, 24099);
//女臉型3(只需要最頭ID)
var F_face_3 = Array(26000, 26003, 26004, 26005, 26006, 26007, 26008, 26009, 26012, 26013, 26014, 26015, 26016, 26017, 26018, 26019, 26020, 26021, 26022, 26023, 26024, 26025, 26026, 26027, 26028, 26029, 26030, 26031, 26032, 26033, 26034, 26035, 26036, 26037, 26038, 26039, 26040, 26041, 26042, 26043, 26044, 26045, 26046, 26047, 26048, 26049, 26050, 26051, 26052, 26053, 26054, 26055, 26056, 26057, 26058, 26059, 26060, 26061, 26062, 26063, 26064, 26065, 26066, 26067, 26068, 26069, 26070, 26071, 26072, 26073, 26074, 26075, 26076, 26077, 26078, 26079, 26082, 26083, 26084, 26085, 26086, 26089, 26090, 26091, 26094, 26095, 26096, 26097, 26099);
//女臉型4(只需要最頭ID)
var F_face_4 = Array(28000, 28001, 28008, 28009, 28010, 28011, 28012, 28014, 28015, 28016, 28017, 28019, 28020, 28021, 28022, 28023, 28024, 28025, 28026, 28027, 28028, 28029, 28030, 28031, 28041, 28042, 28043, 28044, 28045, 28046, 28049, 28050, 28051, 28052, 28053, 28054, 28055, 28056, 28057, 28058, 28059, 28060, 28070, 28071, 28072, 28073, 28074, 28076, 28078, 28079, 28080, 28081, 28082, 28083, 28084, 28085, 28086, 28087, 28088, 28089, 28090, 28091, 28094, 28096, 28097, 28098, 28099);

//男臉型1(只需要最頭ID)
var M_face_1 = Array(20000, 20001, 20002, 20003, 20004, 20005, 20006, 20007, 20008, 20010, 20011, 20012, 20013, 20014, 20015, 20016, 20017, 20018, 20019, 20020, 20021, 20022, 20023, 20024, 20025, 20026, 20027, 20028, 20029, 20030, 20031, 20032, 20033, 20035, 20036, 20037, 20038, 20040, 20041, 20042, 20043, 20044, 20045, 20046, 20047, 20048, 20049, 20050, 20051, 20052, 20053, 20054, 20055, 20056, 20057, 20058, 20059, 20060, 20061, 20062, 20063, 20064, 20065, 20066, 20067, 20068, 20069, 20070, 20073, 20074, 20075, 20076, 20077, 20078, 20080, 20081, 20082, 20083, 20084, 20085, 20086, 20087, 20088, 20089, 20090, 20091, 20092, 20093, 20094, 20095, 20096, 20097, 20098, 20099);
//男臉型2(只需要最頭ID)
var M_face_2 = Array(23000, 23001, 23002, 23003, 23004, 23005, 23006, 23007, 23008, 23010, 23011, 23012, 23013, 23014, 23015, 23016, 23017, 23018, 23019, 23020, 23021, 23022, 23023, 23024, 23025, 23028, 23029, 23030, 23031, 23032, 23033, 23034, 23035, 23038, 23039, 23040, 23041, 23042, 23043, 23044, 23053, 23054, 23055, 23056, 23057, 23058, 23059, 23060, 23061, 23062, 23063, 23064, 23065, 23066, 23067, 23068, 23069, 23070, 23071, 23072, 23073, 23074, 23075, 23076, 23077, 23078, 23079, 23080, 23081, 23082, 23083, 23084, 23085, 23086, 23087, 23088, 23089, 23090, 23092, 23093, 23094, 23095, 23096, 23097, 23098, 23099);
//男臉型3(只需要最頭ID)
var M_face_3 = Array(25000, 25001, 25003, 25004, 25005, 25006, 25007, 25008, 25009, 25010, 25011, 25012, 25013, 25014, 25015, 25016, 25017, 25018, 25019, 25020, 25021, 25022, 25023, 25024, 25025, 25026, 25027, 25028, 25029, 25030, 25031, 25032, 25033, 25034, 25035, 25036, 25037, 25038, 25039, 25040, 25041, 25042, 25043, 25044, 25045, 25046, 25047, 25048, 25049, 25050, 25051, 25052, 25053, 25054, 25055, 25056, 25057, 25058, 25059, 25060, 25061, 25062, 25063, 25064, 25065, 25066, 25067, 25068, 25069, 25070, 25071, 25072, 25073, 25074, 25075, 25076, 25077, 25078, 25079, 25080, 25083, 25084, 25085, 25088, 25089, 25090, 25091, 25093, 25094, 25095, 25096, 25097, 25098, 25099);
//男臉型4(只需要最頭ID)
var M_face_4 = Array(27006, 27007, 27008, 27009, 27010, 27011, 27013, 27014, 27015, 27016, 27017, 27018, 27019, 27020, 27021, 27022, 27023, 27024, 27025, 27035, 27036, 27037, 27038, 27040, 27041, 27044, 27045, 27046, 27047, 27048, 27049, 27050, 27051, 27052, 27053, 27054, 27055, 27064, 27065, 27066, 27067, 27068, 27069, 27070, 27071, 27072, 27073, 27074, 27075, 27076, 27077, 27078, 27079, 27080, 27081, 27082, 27083, 27085, 27086, 27087, 27088, 27089, 27090, 27091, 27092, 27093, 27094, 27095, 27096, 27099);
function start() {
    status = -1;
    action(1, 0, 0);
}

function action(mode, type, selection) {
    if (mode == -1) {
        cm.dispose();
    } else {
        if (status >= 3 && mode == 0) {
            cm.sendOk("#e有需要任何幫助隨時來找我");
            cm.dispose();
            return;
        }
        if (mode == 1)
            status++;
        else {
            cm.sendOk("#e有需要任何幫助隨時來找我");
            cm.dispose();
            return;
        }
        switch (status) {
        case 0:
            if (cm.getPlayer().getGender() == 0) { //男性
				cm.sendSimple("#e这里为您准备了同步版本发型！\r\n\r\n#r- 通用区域#e#n#n#k#r \r\n#L0#肤色\r\n#L19#发色\r\n#L20#眼睛颜色\r\n\r\n\r\n#e#r#l- 发型区域 #e#n#n#k\r\n#r\r\n#b#k#L1##r指定发型第⒈版#r  HOT#k#l\r\n#L2##r指定发型第⒉版#r  HOT#k#l\r\n#L3##r指定发型第⒊版#r  HOT#k#l\r\n#L4##r指定发型第⒋版#r  HOT#k#l\r\n#b#L21##r指定发型第⒌版#r  HOT#k#l\r\n#b#L22##r指定发型第⒍版#r  HOT#k#l \r\n  \r\n#e#r- 整容区域\r\n#b#n\r\n#r#L11#指定脸型第①版  #rHOT#k#l\r\n#r#L12#指定整容第②版  #rHOT#k#l\r\n#r#L13#指定脸型第③版  #rHOT#k#l\r\n#r#L14#指定脸型第④版  #rHOT#k#l\r\n");//\r\n#d#L0#肤色    #b#L11#脸型1#L12#脸型2#L13#脸型3#L14#脸型4     #L9#共通发型1#L10#共通发型2\r\n
		                } else { //女性
				//cm.sendSimple("#e该是时候打扮打扮了！\r\n#r#d#L0#肤色#b#L15#脸型1#L16#脸型2#L17#脸型3#L18#脸型4\r\n#r#L19#发色#L20#眼睛颜色\r\n#b#L5#发型1#L6#发型2#L7#发型3#L8#发型4#b#L23#发型5#b#L24#发型6 \r\n#L9#共通发型1#L10#共通发型2\r\n");//\r\n#d#L0#肤色    #b#L15#脸型1#L16#脸型2#L17#脸型3#L18#脸型4
				cm.sendSimple("#e这里为您准备了同步版本发型！\r\n\r\n#r- 通用区域#e#n#n#k#r \r\n#L0#肤色\r\n#L19#发色\r\n#L20#眼睛颜色\r\n\r\n\r\n#e#r#l- 发型区域 #e#n#n#k\r\n#r\r\n#b#k#L5##r指定发型第⒈版#r  HOT#k#l\r\n#L6##r指定发型第⒉版#r  HOT#k#l\r\n#L7##r指定发型第⒊版#r  HOT#k#l\r\n#L8##r指定发型第⒋版#r  HOT#k#l\r\n#b#L23##r指定发型第⒌版#r  HOT#k#l\r\n#b#L24##r指定发型第⒍版#r  HOT#k#l \r\n  \r\n#e#r- 整容区域\r\n#b#n\r\n#r#L15#指定脸型第①版  #rHOT#k#l\r\n#r#L16#指定整容第②版  #rHOT#k#l\r\n#r#L17#指定脸型第③版  #rHOT#k#l\r\n#r#L18#指定脸型第④版  #rHOT#k#l\r\n");
						}

            break;
        case 1:
            selec_style = selection;
            switch (selec_style) {
            case 0: //膚色
                cm.sendStyle("#e選擇你最喜歡的造型吧！", skin_color);
                break;
            case 1: //男髮1
                cm.sendStyle("#e選擇你最喜歡的造型吧！", M_hair_1);
                break;
            case 2: //男髮2
                cm.sendStyle("#e選擇你最喜歡的造型吧！", M_hair_2);
                break;
            case 3: //男髮3
                cm.sendStyle("#e選擇你最喜歡的造型吧！", M_hair_3);
                break;
            case 4: //男髮4
                cm.sendStyle("#e選擇你最喜歡的造型吧！", M_hair_4);
                break;
			case 21: //男髮5
                cm.sendStyle("#e選擇你最喜歡的造型吧！", M_hair_100);
                break;
			case 22: //男髮6
                cm.sendStyle("#e選擇你最喜歡的造型吧！", M_hair_101);
                break;
			case 23: //女髮5
                cm.sendStyle("#e選擇你最喜歡的造型吧！", F_hair_102);
                break;
							case 24: //女髮6
                cm.sendStyle("#e選擇你最喜歡的造型吧！", F_hair_103);
                break;
            case 5: //女髮1
                cm.sendStyle("#e選擇你最喜歡的造型吧！", F_hair_1);
                break;
            case 6: //女髮2
                cm.sendStyle("#e選擇你最喜歡的造型吧！", F_hair_2);
                break;
            case 7: //女髮3
                cm.sendStyle("#e選擇你最喜歡的造型吧！", F_hair_3);
                break;
            case 8: //女髮4
                cm.sendStyle("#e選擇你最喜歡的造型吧！", F_hair_4);
                break;
            case 9: //共通髮型1
                cm.sendStyle("#e選擇你最喜歡的造型吧！", C_hair_1);
                break;
            case 10: //共通髮型2
                cm.sendStyle("#e選擇你最喜歡的造型吧！", C_hair_2);
                break;
            case 11: //男臉1
                cm.sendStyle("#e選擇你最喜歡的造型吧！", M_face_1);
                break;
            case 12: //男臉2
                cm.sendStyle("#e選擇你最喜歡的造型吧！", M_face_2);
                break;
            case 13: //男臉3
                cm.sendStyle("#e選擇你最喜歡的造型吧！", M_face_3);
                break;
            case 14: //男臉4
                cm.sendStyle("#e選擇你最喜歡的造型吧！", M_face_4);
                break;
            case 15: //女臉1
                cm.sendStyle("#e選擇你最喜歡的造型吧！", F_face_1);
                break;
            case 16: //女臉2
                cm.sendStyle("#e選擇你最喜歡的造型吧！", F_face_2);
                break;
            case 17: //女臉3
                cm.sendStyle("#e選擇你最喜歡的造型吧！", F_face_3);
                break;
            case 18: //女臉4
                cm.sendStyle("#e選擇你最喜歡的造型吧！", F_face_4);
                break;
            case 19: //髮色
                var hair = (cm.getPlayer().getHair()).toString(); //取得玩家髮型ID 轉為字串
                if (hair.slice(-1) >= 1) { //如果字串最尾碼大於1
                    var fin_hair = hair - hair.slice(-1); //髮型ID減去字串最尾碼(還原髮型ID)
                    for (var hair_c = 0; hair_c < 8; hair_c++) {
                        hair_color.push(fin_hair + hair_c); //髮型全髮色陣列
                    }
                    cm.sendStyle("#e選擇你最喜歡的造型吧！", hair_color);
                } else {
                    fin_hair = cm.getPlayer().getHair();
                    for (var hair_c = 0; hair_c < 8; hair_c++) {
                        hair_color.push(fin_hair + hair_c); //髮型全髮色陣列
                    }
                    cm.sendStyle("#e選擇你最喜歡的造型吧！", hair_color);
                }
                break;
            case 20: //眼色
                var eyes = (cm.getPlayer().getFace()).toString(); //取得玩家臉型ID 轉為字串
                if (eyes.slice(-3, -2) >= 1) { //如果字串倒數第三碼大於1
                    var fin_eyes = eyes - (eyes.slice(-3, -2) * 100); //臉型ID減去字串倒數第三碼(還原臉型ID)
                    for (var eyes_c = 0; eyes_c < 9; eyes_c++) {
                        eyes_color.push(fin_eyes + (eyes_c * 100)); //臉型全眼色陣列
                    }
                    cm.sendStyle("#e選擇你最喜歡的造型吧！", eyes_color);
                } else {
                    var fin_eyes = cm.getPlayer().getFace();
                    for (var eyes_c = 0; eyes_c < 9; eyes_c++) {
                        eyes_color.push(fin_eyes + (eyes_c * 100)); //臉型全眼色陣列
                    }
                    cm.sendStyle("#e選擇你最喜歡的造型吧！", eyes_color);
                }
                break;
            }
            break;
        case 2:
            selec_final = selection;
            switch (selec_style) {
            case 0: //膚色
                cm.setSkin(skin_color[selec_final]);
                cm.sendOk("#e享受全新的造型！");
                break;
            case 1: //男髮1
                cm.setHair(M_hair_1[selec_final]);
                cm.sendOk("#e享受全新的造型！");
                break;
            case 2: //男髮2
                cm.setHair(M_hair_2[selec_final]);
                cm.sendOk("#e享受全新的造型！");
                break;
            case 3: //男髮3
                cm.setHair(M_hair_3[selec_final]);
                cm.sendOk("#e享受全新的造型！");
                break;
            case 4: //男髮4
                cm.setHair(M_hair_4[selec_final]);
                cm.sendOk("#e享受全新的造型！");
                break;
			case 21: //男髮4
                cm.setHair(M_hair_100[selec_final]);
                cm.sendOk("#e享受全新的造型！");
                break;
			case 22: //男髮4
                cm.setHair(M_hair_101[selec_final]);
                cm.sendOk("#e享受全新的造型！");
                break
			case 23: //女髮5
                cm.setHair(F_hair_102[selec_final]);
                cm.sendOk("#e享受全新的造型！");
                break
			case 24: //女髮6
                cm.setHair(F_hair_103[selec_final]);
                cm.sendOk("#e享受全新的造型！");
                break
            case 5: //女髮1
                cm.setHair(F_hair_1[selec_final]);
                cm.sendOk("#e享受全新的造型！");
                break;
            case 6: //女髮2
                cm.setHair(F_hair_2[selec_final]);
                cm.sendOk("#e享受全新的造型！");
                break;
            case 7: //女髮3
                cm.setHair(F_hair_3[selec_final]);
                cm.sendOk("#e享受全新的造型！");
                break;
            case 8: //女髮4
                cm.setHair(F_hair_4[selec_final]);
                cm.sendOk("#e享受全新的造型！");
                break;
            case 9: //共通髮型1
                cm.setHair(C_hair_1[selec_final]);
                cm.sendOk("#e享受全新的造型！");
                break;
            case 10: //共通髮型2
                cm.setHair(C_hair_2[selec_final]);
                cm.sendOk("#e享受全新的造型！");
                break;
            case 11: //男臉1
                cm.setFace(M_face_1[selec_final]);
                cm.sendOk("#e享受全新的造型！");
                break;
            case 12: //男臉2
                cm.setFace(M_face_2[selec_final]);
                cm.sendOk("#e享受全新的造型！");
                break;
            case 13: //男臉3
                cm.setFace(M_face_3[selec_final]);
                cm.sendOk("#e享受全新的造型！");
                break;
            case 14: //男臉4
                cm.setFace(M_face_4[selec_final]);
                cm.sendOk("#e享受全新的造型！");
                break;
            case 15: //女臉1
                cm.setFace(F_face_1[selec_final]);
                cm.sendOk("#e享受全新的造型！");
                break;
            case 16: //女臉2
                cm.setFace(F_face_2[selec_final]);
                cm.sendOk("#e享受全新的造型！");
                break;
            case 17: //女臉3
                cm.setFace(F_face_3[selec_final]);
                cm.sendOk("#e享受全新的造型！");
                break;
            case 18: //女臉4
                cm.setFace(F_face_4[selec_final]);
                cm.sendOk("#e享受全新的造型！");
                break;
            case 19: //髮色
                cm.setHair(hair_color[selec_final]);
                cm.sendOk("#e享受全新的造型！");
                break;
            case 20: //眼色
                cm.setFace(eyes_color[selec_final]);
                cm.sendOk("#e享受全新的造型！");
                break;
            }
            cm.dispose();
            break;
        default:
            cm.dispose();
            return;
        }
    }
}

