#pragma once

// ==================== 伤害皮肤系统 v1.0 ====================
// 功能：自定义伤害数字和MISS效果显示
// 特性：支持467+种伤害皮肤，自动MISS资源加载，安全Hook机制
// 状态：稳定运行
// =========================================================

class DamageSkin1
{
public:
	// 系统状态
	static bool isInitialized;
	static int g_nDamageSkin;               // 当前使用的皮肤ID

	// 核心功能
	static void AttachDamageSkinMod();      // 初始化Hook系统
	static void LoadDamageSkin();           // 加载所有伤害皮肤资源

	// 皮肤管理
	static void SendDamageSkinToServer(int skinId);  // 同步皮肤选择到服务器
	static void SetDamageSkin(int skinId);           // 设置伤害皮肤并同步到服务器

	// 调试和监控功能
	static void AttachMissHook();           // MISS Hook (已集成到主函数)
	static void TestMissHook();             // 系统健康检查
	static void TryDifferentMissAddress();  // 地址测试 (已废弃)
};